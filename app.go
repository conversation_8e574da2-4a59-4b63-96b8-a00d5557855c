package main

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/wailsapp/wails/v2/pkg/runtime"
	"github.com/xuri/excelize/v2"
)

// App struct
type App struct {
	ctx    context.Context
	cancel context.CancelFunc
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{}
}

// startup is called when the app starts.
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
	a.ctx, a.cancel = context.WithCancel(ctx)
}

func (a *App) CancelTask() {
	if a.cancel != nil {
		a.cancel()
	}
}

// shutdown is called when the app terminates.
func (a *App) shutdown(ctx context.Context) {}

// --- Structs for event data ---
type SplitResult struct {
	OutputDir     string `json:"outputDir"`
	TotalDataRows int    `json:"totalDataRows"` // 实际数据行数 (不含标题)
	SplitFiles    int    `json:"splitFiles"`
	Success       bool   `json:"success"`
	Message       string `json:"message"`
}

type ApiCallInfo struct {
	FileName string `json:"fileName"`
	Success  bool   `json:"success"`
	Response string `json:"response"`
	Message  string `json:"message"`
}

type ProgressUpdateData struct {
	Percentage    int    `json:"percentage"`
	Status        string `json:"status"`
	CurrentStep   int    `json:"currentStep"`
	TotalSteps    int    `json:"totalSteps"`
	OperationType string `json:"operationType"` // "splitting", "api_call", "general"
}

// --- Helper functions to emit events to frontend ---

func (a *App) sendLog(message string) {
	fullMessage := fmt.Sprintf("[%s] %s", time.Now().Format("15:04:05"), message)
	if a.ctx != nil {
		runtime.EventsEmit(a.ctx, "logMessage", fullMessage)
	} else {
		log.Println("LOG (ctx is nil):", fullMessage) // Fallback for testing
	}
}

func (a *App) sendToast(toastType string, message string) { // type: "success", "error", "info"
	if a.ctx != nil {
		runtime.EventsEmit(a.ctx, "toastMessage", map[string]interface{}{"type": toastType, "message": message})
	} else {
		log.Printf("TOAST (ctx is nil) [%s]: %s\n", toastType, message)
	}
}

func (a *App) updateProgress(currentStep, totalSteps int, statusMessage, operationType string) {
	percentage := 0
	if totalSteps > 0 {
		percentage = int(float64(currentStep) / float64(totalSteps) * 100)
	}
	if a.ctx != nil {
		runtime.EventsEmit(a.ctx, "progressUpdate", ProgressUpdateData{
			Percentage:    percentage,
			Status:        statusMessage,
			CurrentStep:   currentStep,
			TotalSteps:    totalSteps,
			OperationType: operationType,
		})
	} else {
		log.Printf("PROGRESS (ctx is nil): %d%% (%d/%d) - %s - %s\n", percentage, currentStep, totalSteps, statusMessage, operationType)
	}
}

// --- Core Logic: ProcessExcel ---
// filePath: 绝对路径
// nStr: 每份文件的行数 (字符串形式)
// ucid: 用户输入的 UCID
func (a *App) ProcessExcel(filePath string, nStr string, ucid string) {
	a.sendLog(fmt.Sprintf("开始处理任务。文件: %s, 每份行数N: %s, UCID: %s", filepath.Base(filePath), nStr, ucid))

	n, err := strconv.Atoi(nStr)
	if err != nil || n <= 0 {
		errMsg := fmt.Sprintf("错误: N 值 '%s' 无效。必须是大于0的整数。", nStr)
		a.sendLog(errMsg)
		a.sendToast("error", errMsg)
		a.updateProgress(0, 1, "N值无效，处理失败", "general")
		return
	}

	// --- 1. 切分 Excel ---
	a.sendLog("阶段 1: 开始切分 Excel 文件...")
	a.updateProgress(0, 100, "正在准备切分...", "splitting") // Initial splitting progress

	// 创建输出目录: "待导入数据yyyy-MM-dd_HH-mm-ss"
	outputDirName := fmt.Sprintf("待导入数据_%s", time.Now().Format("20060102150405"))
	// 获取程序当前工作目录或用户文档目录等作为基础路径
	// 为简单起见，我们先用当前工作目录。在实际应用中，可能需要更灵活的路径选择。
	// basePath, err := filepath.Abs(filePath)

	basePath := filepath.Dir(filePath)

	a.sendLog(fmt.Sprintf("当前工作目录: %s", basePath))

	// if err != nil {
	// 	errMsg := fmt.Sprintf("错误: 无法获取当前工作目录: %v", err)
	// 	a.sendLog(errMsg)
	// 	a.sendToast("error", "无法获取工作目录")
	// 	a.updateProgress(0, 1, "切分失败", "splitting")
	// 	return
	// }
	outputDir := filepath.Join(basePath, outputDirName)

	if err := os.MkdirAll(outputDir, os.ModePerm); err != nil {
		errMsg := fmt.Sprintf("错误: 创建目录 %s 失败: %v", outputDir, err)
		a.sendLog(errMsg)
		a.sendToast("error", "创建输出目录失败")
		a.updateProgress(0, 1, "切分失败", "splitting")
		return
	}
	a.sendLog(fmt.Sprintf("输出目录已创建: %s", outputDir))

	// 读取源 Excel 文件
	sourceFile, err := excelize.OpenFile(filePath)
	if err != nil {
		errMsg := fmt.Sprintf("错误: 打开 Excel 文件 %s 失败: %v", filePath, err)
		a.sendLog(errMsg)
		a.sendToast("error", "打开源 Excel 文件失败")
		a.updateProgress(0, 1, "切分失败", "splitting")
		return
	}
	defer func() {
		if err := sourceFile.Close(); err != nil {
			a.sendLog(fmt.Sprintf("警告: 关闭源 Excel 文件失败: %v", err))
		}
	}()

	// 假设数据在第一个 Sheet
	sheetName := sourceFile.GetSheetList()[0]
	if sheetName == "" {
		errMsg := "错误: Excel 文件中没有找到任何工作表。"
		a.sendLog(errMsg)
		a.sendToast("error", errMsg)
		a.updateProgress(0, 1, "切分失败", "splitting")
		return
	}

	rows, err := sourceFile.GetRows(sheetName)
	if err != nil {
		errMsg := fmt.Sprintf("错误: 读取工作表 %s 数据失败: %v", sheetName, err)
		a.sendLog(errMsg)
		a.sendToast("error", "读取 Excel 数据失败")
		a.updateProgress(0, 1, "切分失败", "splitting")
		return
	}

	if len(rows) <= 1 { // 至少需要标题行和一行数据
		errMsg := "错误: Excel 文件为空或仅有标题行，无法切分。"
		a.sendLog(errMsg)
		a.sendToast("info", "Excel 文件数据不足，无法切分。")
		a.updateProgress(100, 100, "数据不足，切分结束", "splitting")
		return
	}

	header := rows[0]
	dataRows := rows[1:]
	totalDataRows := len(dataRows)
	numSplitFiles := 0
	if totalDataRows > 0 {
		numSplitFiles = (totalDataRows + n - 1) / n // 向上取整
	}

	a.sendLog(fmt.Sprintf("源文件共有 %d 条数据行 (不含标题)。将切分为 %d 个文件，每个文件 %d 行数据。", totalDataRows, numSplitFiles, n))

	var splitFilePaths []string

	for i := 0; i < numSplitFiles; i++ {
		splitFileProgress := int(float64(i+1) / float64(numSplitFiles) * 100)
		a.updateProgress(splitFileProgress, 100, fmt.Sprintf("正在生成第 %d/%d 个切分文件...", i+1, numSplitFiles), "splitting")

		newExcel := excelize.NewFile()
		newSheetName := "Sheet1" // Wails V3 style
		// newSheetIdx, _ := newExcel.NewSheet(newSheetName) // For older excelize, or just use default
		streamWriter, err := newExcel.NewStreamWriter(newSheetName)
		if err != nil {
			errMsg := fmt.Sprintf("错误: 创建新 Excel Stream Writer 失败 (文件 %d): %v", i+1, err)
			a.sendLog(errMsg)
			// Potentially skip this file or stop
			continue
		}

		// 写标题行
		// excelize Style API requires slice of interface{} for cells.
		headerInterface := make([]interface{}, len(header))
		for j, h := range header {
			headerInterface[j] = h
		}
		if err := streamWriter.SetRow("A1", headerInterface); err != nil {
			a.sendLog(fmt.Sprintf("错误: 写入标题行失败 (文件 %d): %v", i+1, err))
			// continue
		}

		// 写数据行
		startRowIndex := i * n
		endRowIndex := (i + 1) * n
		if endRowIndex > totalDataRows {
			endRowIndex = totalDataRows
		}

		currentDataRows := dataRows[startRowIndex:endRowIndex]
		for rIdx, dataRow := range currentDataRows {
			// excelize Style API requires slice of interface{} for cells.
			dataRowInterface := make([]interface{}, len(dataRow))
			for cIdx, cell := range dataRow {
				dataRowInterface[cIdx] = cell
			}
			cell, _ := excelize.CoordinatesToCellName(1, rIdx+2) // +2 because 1-indexed and header is row 1
			if err := streamWriter.SetRow(cell, dataRowInterface); err != nil {
				a.sendLog(fmt.Sprintf("警告: 写入数据行 %d 失败 (文件 %d): %v", rIdx+1, i+1, err))
			}
		}

		if err := streamWriter.Flush(); err != nil {
			a.sendLog(fmt.Sprintf("错误: Flush Stream Writer 失败 (文件 %d): %v", i+1, err))
			// continue
		}
		// newExcel.SetActiveSheet(newSheetIdx) // For older excelize

		splitFileName := fmt.Sprintf("split_part_%03d.xlsx", i+1)
		fullPath := filepath.Join(outputDir, splitFileName)
		if err := newExcel.SaveAs(fullPath); err != nil {
			errMsg := fmt.Sprintf("错误: 保存切分文件 %s 失败: %v", fullPath, err)
			a.sendLog(errMsg)
			// continue or stop
		} else {
			a.sendLog(fmt.Sprintf("已生成切分文件: %s", fullPath))
			splitFilePaths = append(splitFilePaths, fullPath)
		}
		if err := newExcel.Close(); err != nil { // Close the excelize file instance
			a.sendLog(fmt.Sprintf("警告: 关闭新创建的Excel文件 %s 失败: %v", splitFileName, err))
		}
	}

	splitResultMessage := fmt.Sprintf("Excel 切分完成。输出目录: %s, 总数据行数: %d, 切分文件数: %d", outputDir, totalDataRows, len(splitFilePaths))
	a.sendLog(splitResultMessage)
	a.sendToast("success", fmt.Sprintf("Excel 切分完成! %d 个文件已生成。", len(splitFilePaths)))
	if a.ctx != nil {
		runtime.EventsEmit(a.ctx, "splitComplete", SplitResult{
			OutputDir:     outputDir,
			TotalDataRows: totalDataRows,
			SplitFiles:    len(splitFilePaths),
			Success:       true,
			Message:       splitResultMessage,
		})
	}
	a.updateProgress(100, 100, "切分完成", "splitting")

	// --- 2. 串行调用 API ---
	if len(splitFilePaths) == 0 {
		a.sendLog("没有切分文件可供处理。任务结束。")
		a.sendToast("info", "没有切分文件，操作结束。")
		a.updateProgress(100, 100, "无文件，操作完成", "general") // Overall progress
		return
	}

	a.sendLog(fmt.Sprintf("\n阶段 2: 开始串行调用 API (%d 个文件)...", len(splitFilePaths)))
	totalApiCalls := len(splitFilePaths)
	apiEndpoint := "http://localhost:8011/engine/excel"

	for i, splitFilePath := range splitFilePaths {
		fileName := filepath.Base(splitFilePath)
		a.updateProgress(i, totalApiCalls, fmt.Sprintf("准备调用API: %s (%d/%d)", fileName, i+1, totalApiCalls), "api_call")
		a.sendLog(fmt.Sprintf("正在为文件 %s (%d/%d) 调用 API...", fileName, i+1, totalApiCalls))

		// Prepare multipart form data
		file, err := os.Open(splitFilePath)
		if err != nil {
			errMsg := fmt.Sprintf("错误: 打开待上传文件 %s 失败: %v", fileName, err)
			a.sendLog(errMsg)
			a.sendToast("error", fmt.Sprintf("API处理失败: %s", fileName))
			if a.ctx != nil {
				runtime.EventsEmit(a.ctx, "apiCallResult", ApiCallInfo{FileName: fileName, Success: false, Message: errMsg})
			}
			continue // Skip to next file
		}
		defer file.Close()

		body := &bytes.Buffer{}
		writer := multipart.NewWriter(body)
		part, err := writer.CreateFormFile("file", fileName)
		if err != nil {
			errMsg := fmt.Sprintf("错误: 创建表单文件部分失败 (%s): %v", fileName, err)
			a.sendLog(errMsg)
			writer.Close() // Ensure writer is closed even on error
			a.sendToast("error", fmt.Sprintf("API准备失败: %s", fileName))
			if a.ctx != nil {
				runtime.EventsEmit(a.ctx, "apiCallResult", ApiCallInfo{FileName: fileName, Success: false, Message: errMsg})
			}
			continue
		}
		_, err = io.Copy(part, file)
		if err != nil {
			errMsg := fmt.Sprintf("错误: 复制文件到表单失败 (%s): %v", fileName, err)
			a.sendLog(errMsg)
			writer.Close()
			a.sendToast("error", fmt.Sprintf("API准备失败: %s", fileName))
			if a.ctx != nil {
				runtime.EventsEmit(a.ctx, "apiCallResult", ApiCallInfo{FileName: fileName, Success: false, Message: errMsg})
			}
			continue
		}
		writer.Close() // Must close before NewRequest to set boundary

		req, err := http.NewRequest("POST", apiEndpoint, body)
		if err != nil {
			errMsg := fmt.Sprintf("错误: 创建 HTTP 请求失败 (%s): %v", fileName, err)
			a.sendLog(errMsg)
			a.sendToast("error", fmt.Sprintf("API请求创建失败: %s", fileName))
			if a.ctx != nil {
				runtime.EventsEmit(a.ctx, "apiCallResult", ApiCallInfo{FileName: fileName, Success: false, Message: errMsg})
			}
			continue
		}
		req.Header.Set("Content-Type", writer.FormDataContentType())

		client := &http.Client{Timeout: time.Minute * 2} // 2 minute timeout for API call
		resp, err := client.Do(req)
		apiCallSuccess := false
		var apiResponse string

		if err != nil {
			errMsg := fmt.Sprintf("API 调用失败 (%s): %v", fileName, err)
			a.sendLog(errMsg)
			apiResponse = err.Error()
		} else {
			defer resp.Body.Close()
			respBodyBytes, readErr := io.ReadAll(resp.Body)
			if readErr != nil {
				apiResponse = fmt.Sprintf("读取响应体失败: %v (状态码: %d)", readErr, resp.StatusCode)
				a.sendLog(fmt.Sprintf("API 响应读取失败 (%s): %s", fileName, apiResponse))
			} else {
				apiResponse = string(respBodyBytes)
				if resp.StatusCode >= 200 && resp.StatusCode < 300 {
					apiCallSuccess = true
					a.sendLog(fmt.Sprintf("API 调用成功 (%s): %s. 响应: %s", fileName, resp.Status, apiResponse))
				} else {
					a.sendLog(fmt.Sprintf("API 调用返回错误 (%s): %s. 响应: %s", fileName, resp.Status, apiResponse))
				}
			}
		}

		if a.ctx != nil {
			runtime.EventsEmit(a.ctx, "apiCallResult", ApiCallInfo{
				FileName: fileName,
				Success:  apiCallSuccess,
				Response: apiResponse,
				Message:  fmt.Sprintf("API 调用 %s", map[bool]string{true: "成功", false: "失败"}[apiCallSuccess]),
			})
		}
		a.updateProgress(i+1, totalApiCalls, fmt.Sprintf("API调用完成: %s (%d/%d)", fileName, i+1, totalApiCalls), "api_call")

		if i < totalApiCalls-1 {
			a.sendLog(fmt.Sprintf("等待 %d 秒后调用下一个文件...", 10))
			time.Sleep(10 * time.Second)
		}
	}

	a.sendLog("所有 API 调用完成。")
	a.sendToast("success", "所有操作已成功完成！")
	a.updateProgress(totalApiCalls, totalApiCalls, "全部任务完成", "general") // Overall progress
	if a.ctx != nil {
		runtime.EventsEmit(a.ctx, "allOperationsComplete", nil)
	}
}

// 新增：文件选择方法
func (a *App) SelectFile() (string, error) {
	selection, err := runtime.OpenFileDialog(a.ctx, runtime.OpenDialogOptions{
		Title: "选择 Excel 文件",
		Filters: []runtime.FileFilter{
			{
				DisplayName: "Excel (*.xlsx, *.xls)",
				Pattern:     "*.xlsx;*.xls",
			},
		},
	})
	return selection, err
}
