{"$schema": "https://wails.io/schemas/config.v2.json", "name": "sheet-genie", "outputfilename": "sheet-genie", "frontend:install": "npm install", "frontend:build": "npm run build", "frontend:dev:watcher": "npm run dev", "frontend:dev:serverUrl": "auto", "author": {"name": "oxyuan", "email": "<EMAIL>"}, "options": {"mac": {"applicationCategoryType": "public.app-category.utilities", "entitlements": "build/entitlements.plist", "fileAssociations": []}, "windows": {"fileAssociations": []}, "linux": {"fileAssociations": []}}}