# 房配精灵 · SheetGenie

一个基于 Go 和 Wails 框架构建的精致桌面工具，用于对 Excel 数据进行自动切分、批量调用接口并实时记录日志，适用于数据清洗、批量导入等场景。

---

## ✨ 功能概览

1. **Excel 文件导入与切分**
   - 支持选择任意 `.xlsx` 文件。
   - 默认按每 1000 行（含表头）切分为多个文件。
   - 自动在当前目录下创建切分输出目录，命名规则：  
     `待处理数据_yyyyMMddHHmmss`

2. **批量串行 API 上传**
   - 自动遍历切分目录下所有 Excel 文件。
   - 串行调用 API（每次间隔 10 秒）。
   - 支持 UCID 自定义输入，自动嵌入请求头。

3. **实时进度与日志展示**
   - 中部进度条实时显示上传进度。
   - 底部日志面板实时追加操作记录，支持手动滚动查看历史日志。

---

## 🖥️ 界面布局

- **顶部操作区（2/5 高度）**  
  文件选择、UCID 输入、开始按钮。

- **中部进度区（1/5 高度）**  
  横向进度条，展示上传完成比例。

- **底部日志区（2/5 高度）**  
  多行文本框，支持滚动查看日志，默认自动滚动到底部。

界面简洁、清爽，基于现代 UI 美学构建。

---

## 🚀 使用方法

1. 打开应用，点击 `选择文件` 按钮导入 Excel。
2. 输入 UCID。
3. 点击 `开始切分`：
   - 程序将自动完成 Excel 切分。
   - 切分完成后自动串行上传，每个文件调用一次 API。
   - 上传过程会自动更新进度条与日志面板。

---

## 📦 请求接口说明

接口地址：

```

POST [http://localhost:8080/xxx](http://localhost:8080/xxx)

```

请求头需包含：

```
UCID: <你的 UCID>
```

请求体：

```
Content-Type: multipart/form-data
file=@<切分后的 Excel 文件>
```

---

## 🛠️ 技术栈

- **Go (Golang)**：后端逻辑处理、文件操作、HTTP 请求。
- **Wails**：Go + Web 的桌面应用开发框架。
- **Excelize**：读取与写入 Excel 文件。
- **HTML/CSS/JS (Vue/React)**：构建现代化 UI 界面。
- **Wails Runtime API**：实现 Go 与前端之间的事件通信。

---

## 📁 示例目录结构

```

待处理数据_yyyyMMddHHmmss/
├── split\_1.xlsx
├── split\_2.xlsx
├── ...

````

---

## 📄 开发与构建

```bash
# 安装依赖
go install github.com/wailsapp/wails/v2/cmd/wails@latest

# 编译运行
wails dev

# 打包构建
wails build
````

---

## 📢 注意事项

* 原始 Excel 首行应为表头。
* 单个 Excel 文件最多导出约 N=1000 行数据 + 表头。
* 接口调用后延时 10 秒自动处理下一个文件，避免服务端限流。
* 仅支持 `.xlsx` 格式，暂不支持 `.xls` 或其他表格格式。

---

## 🧑‍💻 作者

大山（Dashan）
欢迎交流反馈与建议。

---