<script>
    import { onMount, onDestroy } from 'svelte';
    // For Wails v2.10+，runtime和Go方法只能通过window对象访问
    // e.g., window.runtime.OpenFileDialog, window.go.main.App.ProcessExcel

    let selectedFileFullPath = null;
    let selectedFileName = "未选择文件";
    let nRows = 1000;
    let ucid = "1000000031076851"; // Default UCID
    let logs = [];
    let logAreaElement;
    let userScrolledLog = false;

    let progressPercent = 0;
    let progressStatus = "空闲";
    let currentOperationType = "general"; // "splitting", "api_call", "general"

    let isProcessing = false;

    // Toast notifications
    let toasts = []; // Array of {id, type, message}
    let toastIdCounter = 0;

    let runtimeReady = false; // 新增一个状态来跟踪runtime是否就绪

    function addToast(type, message) {
        const id = toastIdCounter++;
        toasts = [...toasts, { id, type, message }];
        setTimeout(() => {
            toasts = toasts.filter(t => t.id !== id);
        }, 5000); // Auto-remove toast after 5 seconds
    }

    async function handleSelectFile() {
        if (isProcessing) return;
        try {
            // @ts-ignore
            const filePath = await window.go.main.App.SelectFile();
            if (filePath) {
                selectedFileFullPath = filePath;
                selectedFileName = filePath.split(/[\\/]/).pop();
                internalLog(`文件已选择: ${selectedFileFullPath}`);
                addToast("info", `已选择文件: ${selectedFileName}`);
            } else {
                addToast("error", "未选择文件或操作已取消。");
            }
        } catch (err) {
            addToast("error", "选择文件失败。");
            internalLog(`选择文件错误: ${err}`);
        }
    }

    async function startMainProcess() {
        if (!selectedFileFullPath) {
            addToast("error", "请先选择一个 Excel 文件。");
            internalLog("错误: 未选择 Excel 文件。");
            return;
        }
        if (!ucid.trim()) {
            addToast("error", "请输入 UCID。");
            internalLog("错误: UCID 不能为空。");
            return;
        }
        if (nRows <= 0) {
            addToast("error", "每份文件行数 (N) 必须大于 0。");
            internalLog("错误: N 值无效。");
            return;
        }

        isProcessing = true;
        progressPercent = 0;
        progressStatus = "正在初始化...";
        currentOperationType = "general";
        logs = []; // Clear previous logs
        internalLog("开始处理流程...");

        try {
            // @ts-ignore
            await window.go.main.App.ProcessExcel(selectedFileFullPath, String(nRows), ucid);
            // Go backend will emit 'allOperationsComplete' or handle errors internally
        } catch (error) {
            internalLog(`处理时发生严重错误: ${error}`);
            addToast("error", `处理错误: ${error}`);
            progressStatus = "处理失败";
            isProcessing = false;
        }
    }

    function internalLog(message) {
        // No timestamp needed here as Go backend adds it
        logs = [...logs, message];
        scrollToLogBottom();
    }

    function handleLogScroll() {
        if (logAreaElement) {
            const { scrollTop, scrollHeight, clientHeight } = logAreaElement;
            // If scrolled up more than a few pixels from the bottom, consider it user-initiated scroll
            userScrolledLog = scrollHeight - clientHeight - scrollTop > 10;
        }
    }

    function scrollToLogBottom() {
        if (logAreaElement && !userScrolledLog) {
            // Use timeout to ensure DOM has updated
            setTimeout(() => {
                logAreaElement.scrollTop = logAreaElement.scrollHeight;
            }, 0);
        }
    }

    let unsubscribeLogMessage;
    let unsubscribeToastMessage;
    let unsubscribeProgressUpdate;
    let unsubscribeSplitComplete;
    let unsubscribeApiCallResult;
    let unsubscribeAllOperationsComplete;

    onMount(() => {
        // @ts-ignore
        if (window.runtime) {
            // @ts-ignore
            unsubscribeLogMessage = window.runtime.EventsOn("logMessage", (message) => {
                internalLog(message);
            });

            // @ts-ignore
            unsubscribeToastMessage = window.runtime.EventsOn("toastMessage", (data) => {
                addToast(data.type, data.message);
            });

            // @ts-ignore
            unsubscribeProgressUpdate = window.runtime.EventsOn("progressUpdate", (data) => {
                progressPercent = data.percentage;
                progressStatus = `${data.status}`; // Go already formats step count if needed
                currentOperationType = data.operationType;
                if (data.percentage === 100 && (data.operationType === "splitting" || data.operationType === "api_call")){
                    // Don't mark as fully complete if it's just one phase
                } else if (data.percentage === 100 && data.operationType === "general" && data.status.includes("全部")) {
                     isProcessing = false; // Only set false on final completion
                }
            });

            // @ts-ignore
            unsubscribeSplitComplete = window.runtime.EventsOn("splitComplete", (data) => {
                // Log message already sent from Go, toast also.
                // This event is more for potential state changes if needed.
            });

            // @ts-ignore
            unsubscribeApiCallResult = window.runtime.EventsOn("apiCallResult", (data) => {
                // Log and toast messages are handled by the Go backend via generic events.
                // This specific event could be used for more detailed UI updates per API call if needed.
            });

            // @ts-ignore
            unsubscribeAllOperationsComplete = window.runtime.EventsOn("allOperationsComplete", () => {
                progressPercent = 100;
                progressStatus = "所有操作已成功完成！";
                currentOperationType = "general";
                isProcessing = false;
                // Toast already sent from Go.
            });
        } else {
            console.error("Wails runtime not available. Running in browser mode?");
            internalLog("错误: Wails runtime 未初始化。");
        }
    });

    onDestroy(() => {
        // Clean up event listeners
        if (unsubscribeLogMessage) unsubscribeLogMessage();
        if (unsubscribeToastMessage) unsubscribeToastMessage();
        if (unsubscribeProgressUpdate) unsubscribeProgressUpdate();
        if (unsubscribeSplitComplete) unsubscribeSplitComplete();
        if (unsubscribeApiCallResult) unsubscribeApiCallResult();
        if (unsubscribeAllOperationsComplete) unsubscribeAllOperationsComplete();
    });

</script>

<div id="app-container">
    <div class="section controls-area">
        <h2>操作控件</h2>
        <div class="file-selection-group">
            <button on:click={handleSelectFile} disabled={isProcessing}>选择 Excel 文件</button>
            <span class="selected-file-name" title={selectedFileFullPath || ''}>{selectedFileName}</span>
        </div>

        <div class="input-row">
            <div class="input-group">
                <label for="nRowsInput">每份文件行数 (N, 默认1000, 含标题则 N+1):</label>
                <input type="number" id="nRowsInput" bind:value={nRows} min="1" disabled={isProcessing}>
            </div>
            <div class="input-group">
                <label for="ucidInput">UCID:</label>
                <input type="text" id="ucidInput" bind:value={ucid} placeholder="请输入 UCID" disabled={isProcessing}>
            </div>
        </div>
        <button class="main-action-button" on:click={startMainProcess} disabled={isProcessing || !selectedFileFullPath}>
            {#if isProcessing}
                <svg aria-hidden="true" role="status" style="display:inline; width:1em; height:1em; margin-right:0.5em;" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0492C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5424 39.6781 93.9676 39.0409Z" fill="#FFF"/></svg>
                处理中...
            {:else}
                开始处理 (切分与API调用)
            {/if}
        </button>
    </div>

    <div class="section progress-area">
        <h2>处理进度</h2>
        <div class="progress-bar-container">
            <div class="progress-bar" style="width: {progressPercent}%;">
                {progressPercent}%
            </div>
        </div>
        <div class="progress-status">
            {#if currentOperationType === 'splitting'}
                切分进度: {progressStatus}
            {:else if currentOperationType === 'api_call'}
                API 调用进度: {progressStatus}
            {:else}
                {progressStatus}
            {/if}
        </div>
    </div>

    <div class="section log-area">
        <h2>日志输出</h2>
        <div class="log-display" bind:this={logAreaElement} on:scroll={handleLogScroll}>
            {#each logs as logItem, i (i)}
                <div>{logItem}</div>
            {/each}
        </div>
    </div>
</div>

<div class="toast-notifications">
    {#each toasts as toast (toast.id)}
        <div class="toast {toast.type}" role="alert">
            {toast.message}
        </div>
    {/each}
</div>