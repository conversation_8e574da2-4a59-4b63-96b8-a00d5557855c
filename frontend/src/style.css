/* Reset and base styles */
:root {
    --primary-color: #007bff; /* Blue */
    --primary-hover-color: #0056b3;
    --secondary-color: #6c757d; /* Gray */
    --success-color: #28a745; /* Green */
    --danger-color: #dc3545; /* Red */
    --info-color: #17a2b8; /* Teal */
    --light-bg: #f8f9fa;
    --dark-log-bg: #272822; /* Monokai-like dark */
    --dark-log-text: #f8f8f2;
    --border-color: #dee2e6;
    --text-color: #212529;
    --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --border-radius: 0.3rem; /* Slightly more rounded */
    --box-shadow: 0 .125rem .25rem rgba(0,0,0,.075);
}

body {
    font-family: var(--font-family);
    margin: 0;
    background-color: #eef2f7; /* Softer background */
    color: var(--text-color);
    overflow: hidden;
    height: 100vh;
    display: flex;
    flex-direction: column;
    font-size: 15px; /* Base font size */
}

/* Main application container */
#app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding: 1.25rem; /* 20px */
    box-sizing: border-box;
    gap: 1rem; /* 16px spacing between sections */
}

.section {
    background-color: #ffffff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.25rem;
    display: flex;
    flex-direction: column;
}
.section h2 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.25em; /* ~20px */
    color: #343a40;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.5rem;
}

/* Layout distribution */
.controls-area { flex: 2; }
.progress-area { flex: 1; justify-content: center; }
.log-area { flex: 2; overflow: hidden; } /* Key for log scrolling */

/* Controls Area styling */
.input-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    align-items: flex-end; /* Align labels and inputs nicely */
}
.input-group {
    flex: 1; /* Distribute space equally */
    display: flex;
    flex-direction: column;
}
.input-group label {
    margin-bottom: 0.3rem;
    font-weight: 500;
    color: #495057;
    font-size: 0.9em;
}
.input-group input[type="text"],
.input-group input[type="number"] {
    padding: 0.6rem 0.75rem; /* ~10px 12px */
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95em;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}
.input-group input[type="text"]:focus,
.input-group input[type="number"]:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Buttons */
button {
    padding: 0.6rem 1.25rem; /* ~10px 20px */
    font-size: 0.95em;
    font-weight: 500;
    color: #fff;
    background-color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}
button:hover {
    background-color: var(--primary-hover-color);
    border-color: var(--primary-hover-color);
}
button:disabled {
    background-color: #adb5bd;
    border-color: #adb5bd;
    cursor: not-allowed;
    opacity: 0.7;
}
.controls-area .main-action-button {
    align-self: flex-start; /* Don't stretch full width */
}
.file-selection-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}
.file-selection-group button {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}
.file-selection-group button:hover {
    background-color: #5a6268;
    border-color: #545b62;
}
.selected-file-name {
    flex-grow: 1;
    font-style: italic;
    color: #555;
    background-color: var(--light-bg);
    padding: 0.6rem;
    border-radius: var(--border-radius);
    border: 1px dashed var(--border-color);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 0.85em;
}

/* Progress Area */
.progress-bar-container {
    width: 100%;
    background-color: #e9ecef;
    border-radius: var(--border-radius);
    height: 1.75rem; /* ~28px */
    overflow: hidden;
    margin-bottom: 0.5rem;
    border: 1px solid #ced4da;
}
.progress-bar {
    width: 0%;
    height: 100%;
    background-color: var(--success-color);
    color: white;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9em;
    transition: width 0.3s ease-out;
}
.progress-status {
    font-size: 0.9em;
    color: #495057;
    text-align: center;
    height: 1.5em; /* Reserve space to prevent layout shift */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Log Area */
.log-display {
    flex-grow: 1;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    overflow-y: auto; /* Enable vertical scroll */
    background-color: var(--dark-log-bg);
    color: var(--dark-log-text);
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
    font-size: 0.85em;
    white-space: pre-wrap;
    word-break: break-all; /* Break long lines */
    scroll-behavior: smooth; /* Smooth auto-scroll */
}
.log-display div {
    padding: 2px 0;
    /* Optional: border-bottom: 1px dotted #444; */
}
.log-display div:last-child {
    /* border-bottom: none; */
}

/* Toast Notifications */
.toast-notifications {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column-reverse; /* New toasts appear on top */
    gap: 10px;
}
.toast {
    padding: 10px 20px;
    border-radius: var(--border-radius);
    color: white;
    box-shadow: 0 0.25rem 0.75rem rgba(0,0,0,.1);
    opacity: 0;
    transform: translateX(100%);
    animation: slideInToast 0.5s forwards, fadeOutToast 0.5s 4.5s forwards; /* Slide in, then fade out after 4s */
    min-width: 250px;
}
.toast.success { background-color: var(--success-color); }
.toast.error { background-color: var(--danger-color); }
.toast.info { background-color: var(--info-color); }

@keyframes slideInToast {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
@keyframes fadeOutToast {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}